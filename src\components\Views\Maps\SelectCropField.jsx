import React, { useContext, useEffect, useState } from 'react'
import {
	Button,
	Select,
	MenuItem,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	InputLabel,
	FormControl,
	Typography,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox,
  TextField,
  IconButton
  } from '@material-ui/core';
import { Edit as EditIcon } from '@material-ui/icons';
import * as toGeoJSON from 'togeojson';
import JSZip from 'jszip';
import { UserContext } from '../../../context/UserProvider';
import { db } from '../../../config/firebase';


export const SelectCropField = ({ setGeoJsonData,setDrawPolygon,setDrawingMode,setSelectedPolygon,setImageLoaded,setDateOfImage,
  setIndiceName,setTypeAddPolygon, typeAddPolygon,drawnPolygon,setDrawnPolygon,areaInHa,savedPolygonsData,clearDisplayedPolygon }) => {
	const [open, setOpen] = useState(false);
  const { usuario } = useContext(UserContext)
  const savePolygonDataUrl = process.env.REACT_APP_SAVE_NEW_POLYGON_DATA;
  const [selectorValue, setSelectorValue] = useState('');
  const [namesOfPolygons, setNamesOfPolygons] = useState([]);
  const [indiceSeleceted, setIndiceSeleceted] = useState("NDVI");

  // Nuevos estados para manejar la lista de polígonos del archivo
  const [polygonList, setPolygonList] = useState([]);
  const [showPolygonList, setShowPolygonList] = useState(false);
  const [editingIndex, setEditingIndex] = useState(-1);
  const [tempName, setTempName] = useState('');

	const handleOpen = () => {
		setOpen(true);
	};

	const handleClose = () => {
		setOpen(false);
    setShowPolygonList(false);
    setPolygonList([]);
    setEditingIndex(-1);
    setTempName('');
	};

	const handleSelectorChange = (event) => {
		// Limpiar polígono anterior si existe
    if (clearDisplayedPolygon) {
      clearDisplayedPolygon();
    }

    setSelectorValue(event.target.value);
    setSelectedPolygon(event.target.value);
    setImageLoaded(false)
    setDateOfImage('')
    // Establecer el tipo como "saved" para polígonos guardados de Firebase
    setTypeAddPolygon("saved");
	};

  const handleIndiceSelect = (event) => {
    setIndiceSeleceted(event.target.value)
    setIndiceName(event.target.value)
    setImageLoaded(false)
    setDateOfImage('')
  }

  // Funciones para manejar la lista de polígonos
  const handlePolygonToggle = (index) => {
    const updatedList = [...polygonList];
    updatedList[index].selected = !updatedList[index].selected;
    setPolygonList(updatedList);
  };

  const handleEditName = (index) => {
    setEditingIndex(index);
    setTempName(polygonList[index].name);
  };

  const handleSaveName = (index) => {
    const updatedList = [...polygonList];
    updatedList[index].name = tempName;
    setPolygonList(updatedList);
    setEditingIndex(-1);
    setTempName('');
  };

  const handleCancelEdit = () => {
    setEditingIndex(-1);
    setTempName('');
  };

  const handleProcessSelectedPolygons = async () => {
    const selectedPolygons = polygonList.filter(polygon => polygon.selected);

    if (selectedPolygons.length === 0) {
      alert('Por favor selecciona al menos un polígono para procesar.');
      return;
    }

    const polygonsToSend = selectedPolygons.map(polygon => ({
      polygonName: polygon.name,
      geoJsonData: polygon.geoJsonData,
      timestamp: new Date().toISOString()
    }));
    const dataToSend = JSON.stringify({
      username: usuario.username,
      data: polygonsToSend
    });
    console.log("Esto es dataToSend:",dataToSend)


    try {
      // Enviar a Cloud Function
      const response = await fetch(savePolygonDataUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: usuario.username,
          data: polygonsToSend
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Polígonos guardados exitosamente:', result);

      // Actualizar el estado con los polígonos seleccionados
      const geoJsonToSet = {
        type: "FeatureCollection",
        features: selectedPolygons.map(polygon => polygon.geoJsonData)
      };

      setGeoJsonData(geoJsonToSet);
      setNamesOfPolygons(selectedPolygons.map(polygon => polygon.name));
      setShowPolygonList(false);
      setOpen(false);

      alert('Polígonos procesados y guardados exitosamente.');

    } catch (error) {
      console.error('Error al procesar polígonos:', error);
      alert('Error al procesar los polígonos. Por favor intenta de nuevo.');
    }
  };

  const onFileChange = async(event) => {
    const file = event.target.files[0];
    if (drawnPolygon) {
      drawnPolygon.setMap(null);
      setDrawnPolygon(null); // Limpia la referencia
    }

    if (file) {
      setTypeAddPolygon("file");
      if (file.name.endsWith('.kml')) {
        const reader = new FileReader();

        reader.onload = async function (e) {
          const text = e.target.result;

          // Parsear el contenido KML a DOM
          const parser = new DOMParser();
          const kmlDoc = parser.parseFromString(text, 'text/xml');

          // Convertir KML a GeoJSON
          const geoJson = toGeoJSON.kml(kmlDoc);
          const fileDataArray = geoJson.features;
          const polygonArray = [];

          fileDataArray.forEach((item) => {
            if (item.geometry.type === "Polygon") {
              polygonArray.push({
                name: item.properties.name || `Polígono ${polygonArray.length + 1}`,
                geoJsonData: item,
                selected: true // Por defecto seleccionados
              });
            }
          });

          setPolygonList(polygonArray);
          setShowPolygonList(true);
        };

        reader.readAsText(file);
      } else if (file.name.endsWith('.kmz')) {
        const reader = new FileReader();

        reader.onload = function (e) {
          const arrayBuffer = e.target.result;

          JSZip.loadAsync(arrayBuffer).then(async function (zip) {
            // Asume que el primer archivo en el zip es el archivo KML
            const kmlFileName = Object.keys(zip.files)[0];
            zip.files[kmlFileName]
              .async('string')
              .then(async function (kmlText) {
                const parser = new DOMParser();
                const kmlDoc = parser.parseFromString(kmlText, 'text/xml');

                // Convertir KML a GeoJSON
                const geoJson = toGeoJSON.kml(kmlDoc);
                const fileDataArray = geoJson.features;
                const polygonArray = [];

                fileDataArray.forEach((item) => {
                  if (item.geometry.type === "Polygon") {
                    polygonArray.push({
                      name: item.properties.name || `Polígono ${polygonArray.length + 1}`,
                      geoJsonData: item,
                      selected: true // Por defecto seleccionados
                    });
                  }
                });

                setPolygonList(polygonArray);
                setShowPolygonList(true);
              });
          });
        };

        reader.readAsArrayBuffer(file);
      } else {
        alert('Formato de archivo no soportado.');
      }
    }
  };

  useEffect(() => {
    if(savedPolygonsData.length !== 0) {
      const names = savedPolygonsData.map(item => item.polygonName)
      setNamesOfPolygons(names)
    }
  },[savedPolygonsData])
	
  return (
	  <div>
      <div style={{ textAlign: 'center' }}>
        <Button variant="contained" size='small' color="primary" onClick={handleOpen}>
          Agregar campo
        </Button>
      </div>

      <div style={{ marginBottom: "5px", marginTop: "5px"}}>
        <Divider />
      </div>
        
      {namesOfPolygons.length !== 0 && (
        
        <div style={{ marginTop: "15px", marginBottom: "15px"}}>
        <FormControl style={{ width: '100%' }}>
          <InputLabel id="mi-selector-label">Elige un campo</InputLabel>
          <Select
            labelId="mi-selector-label"
            id="mi-selector"
            value={selectorValue}
            onChange={handleSelectorChange}
            disabled={namesOfPolygons.length === 0}
          >
            {namesOfPolygons?.map((name,index) => (
              <MenuItem key={index} value={name}>{name}</MenuItem>
            ))}
          </Select>
        </FormControl>
        </div>
      )}

      {areaInHa !== 0 && (
        <div style={{ marginBottom: "5px", marginTop: "5px"}}>
          <Typography>
            Área: {areaInHa}ha
          </Typography>
        </div>
        
        
      )}

        {typeAddPolygon !== "" && (
          <div style={{ marginTop: "15px", marginBottom: "15px"}}>
          <FormControl style={{ width: '100%' }}>
            <InputLabel id="indiceName-label">Elige un índice</InputLabel>
            <Select
              labelId="indiceSelect"
              id="indice-name"
              value={indiceSeleceted}
              onChange={handleIndiceSelect}
              // disabled={namesOfPolygons.length === 0}
            >
              <MenuItem value={"NDVI"}>NDVI</MenuItem>
              <MenuItem value={"NDMI"}>NDMI</MenuItem>
              <MenuItem value={"MSAVI"}>MSAVI</MenuItem>
            </Select>
          </FormControl>
          </div>
        )}
      
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>Agregar Campo</DialogTitle>
        <DialogContent>
          {!showPolygonList ? (
            <>
              <Typography variant="subtitle1">Seleccione una opción:</Typography>

              {/* Opción para subir archivo */}
              <input
                accept=".kml,.kmz"
                style={{ display: 'none' }}
                id="subir-archivo"
                type="file"
                onChange={onFileChange}
              />
              <label htmlFor="subir-archivo">
                <Button variant="contained" component="span" color="primary" style={{ marginTop: 16 }}>
                  Subir archivo (.kml, .kmz)
                </Button>
              </label>

              {/* Opción para dibujar polígono */}
              <Button
                variant="contained"
                color="secondary"
                style={{ marginTop: 16, marginLeft: 16 }}
                onClick={() => {
                  // Lógica para dibujar polígono
                  setDrawPolygon(true)
                  setDrawingMode('polygon')
                  console.log('Dibujar polígono');
                  setTypeAddPolygon("drawing");
                  handleClose();
                }}
              >
                Dibujar polígono
              </Button>
            </>
          ) : (
            <>
              <Typography variant="h6" style={{ marginBottom: 16 }}>
                Polígonos encontrados en el archivo
              </Typography>
              <Typography variant="body2" style={{ marginBottom: 16, color: '#666' }}>
                Edita los nombres y selecciona los polígonos que deseas agregar:
              </Typography>

              <List style={{ maxHeight: 400, overflow: 'auto' }}>
                {polygonList.map((polygon, index) => (
                  <ListItem key={index} divider>
                    <Checkbox
                      checked={polygon.selected}
                      onChange={() => handlePolygonToggle(index)}
                      color="primary"
                    />

                    {editingIndex === index ? (
                      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                        <TextField
                          value={tempName}
                          onChange={(e) => setTempName(e.target.value)}
                          variant="outlined"
                          size="small"
                          style={{ marginRight: 8, flex: 1 }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleSaveName(index);
                            }
                          }}
                        />
                        <Button
                          size="small"
                          color="primary"
                          onClick={() => handleSaveName(index)}
                          style={{ marginRight: 4 }}
                        >
                          Guardar
                        </Button>
                        <Button
                          size="small"
                          onClick={handleCancelEdit}
                        >
                          Cancelar
                        </Button>
                      </div>
                    ) : (
                      <>
                        <ListItemText
                          primary={polygon.name}
                          style={{ flex: 1 }}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => handleEditName(index)}
                            size="small"
                          >
                            <EditIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </>
                    )}
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </DialogContent>
        <DialogActions>
          {showPolygonList ? (
            <>
              <Button onClick={() => setShowPolygonList(false)} color="default">
                Volver
              </Button>
              <Button
                onClick={handleProcessSelectedPolygons}
                color="primary"
                variant="contained"
                disabled={polygonList.filter(p => p.selected).length === 0}
              >
                Procesar Seleccionados ({polygonList.filter(p => p.selected).length})
              </Button>
            </>
          ) : (
            <Button onClick={handleClose} color="default">
              Cancelar
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </div>
  )
}
